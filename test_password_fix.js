/**
 * 测试密码修复的脚本
 * 验证不同类型用户的密码验证是否正常工作
 */

// 模拟前端哈希函数
async function hashPassword(password) {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

// 模拟后端哈希函数（与前端相同）
async function backendHashPassword(password) {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

// 模拟新的密码验证函数
async function verifyPassword(password, hashedPassword) {
    // 如果数据库中密码为空，说明是OAuth2用户，不允许密码登录
    if (!hashedPassword || hashedPassword === '') {
        return false;
    }
    
    // 方式1：双重哈希验证（兼容现有普通注册用户）
    const doubleHashedInput = await backendHashPassword(password);
    if (doubleHashedInput === hashedPassword) {
        return true;
    }
    
    // 方式2：单次哈希验证（兼容管理员创建的用户）
    // 前端已经哈希过的密码直接与数据库比较
    if (password === hashedPassword) {
        return true;
    }
    
    return false;
}

async function testPasswordScenarios() {
    console.log('🔍 测试密码验证修复...\n');
    
    const testPassword = 'TestPassword123';
    
    // 场景1：OAuth2用户（密码为空）
    console.log('📋 场景1：OAuth2用户');
    const oauth2DbPassword = '';
    const oauth2LoginAttempt = await hashPassword(testPassword);
    const oauth2Result = await verifyPassword(oauth2LoginAttempt, oauth2DbPassword);
    console.log(`  数据库密码: "${oauth2DbPassword}"`);
    console.log(`  登录尝试: ${oauth2LoginAttempt}`);
    console.log(`  验证结果: ${oauth2Result ? '✅ 通过' : '❌ 拒绝'} (预期: 拒绝)`);
    console.log('');
    
    // 场景2：普通注册用户（双重哈希）
    console.log('📋 场景2：普通注册用户（双重哈希）');
    const frontendHash = await hashPassword(testPassword);
    const normalUserDbPassword = await backendHashPassword(frontendHash);
    const normalUserLoginAttempt = await hashPassword(testPassword);
    const normalUserResult = await verifyPassword(normalUserLoginAttempt, normalUserDbPassword);
    console.log(`  前端哈希: ${frontendHash}`);
    console.log(`  数据库密码: ${normalUserDbPassword}`);
    console.log(`  登录尝试: ${normalUserLoginAttempt}`);
    console.log(`  验证结果: ${normalUserResult ? '✅ 通过' : '❌ 拒绝'} (预期: 通过)`);
    console.log('');
    
    // 场景3：管理员创建的用户（旧版本 - 单次哈希）
    console.log('📋 场景3：管理员创建的用户（旧版本 - 单次哈希）');
    const adminCreatedOldDbPassword = await hashPassword(testPassword);
    const adminCreatedOldLoginAttempt = await hashPassword(testPassword);
    const adminCreatedOldResult = await verifyPassword(adminCreatedOldLoginAttempt, adminCreatedOldDbPassword);
    console.log(`  数据库密码: ${adminCreatedOldDbPassword}`);
    console.log(`  登录尝试: ${adminCreatedOldLoginAttempt}`);
    console.log(`  验证结果: ${adminCreatedOldResult ? '✅ 通过' : '❌ 拒绝'} (预期: 通过)`);
    console.log('');
    
    // 场景4：管理员创建的用户（新版本 - 双重哈希）
    console.log('📋 场景4：管理员创建的用户（新版本 - 双重哈希）');
    const adminFrontendHash = await hashPassword(testPassword);
    const adminCreatedNewDbPassword = await backendHashPassword(adminFrontendHash);
    const adminCreatedNewLoginAttempt = await hashPassword(testPassword);
    const adminCreatedNewResult = await verifyPassword(adminCreatedNewLoginAttempt, adminCreatedNewDbPassword);
    console.log(`  前端哈希: ${adminFrontendHash}`);
    console.log(`  数据库密码: ${adminCreatedNewDbPassword}`);
    console.log(`  登录尝试: ${adminCreatedNewLoginAttempt}`);
    console.log(`  验证结果: ${adminCreatedNewResult ? '✅ 通过' : '❌ 拒绝'} (预期: 通过)`);
    console.log('');
    
    // 总结
    console.log('📊 测试总结:');
    console.log(`  OAuth2用户: ${oauth2Result ? '❌ 错误' : '✅ 正确'}`);
    console.log(`  普通注册用户: ${normalUserResult ? '✅ 正确' : '❌ 错误'}`);
    console.log(`  管理员创建用户(旧): ${adminCreatedOldResult ? '✅ 正确' : '❌ 错误'}`);
    console.log(`  管理员创建用户(新): ${adminCreatedNewResult ? '✅ 正确' : '❌ 错误'}`);
    
    const allPassed = !oauth2Result && normalUserResult && adminCreatedOldResult && adminCreatedNewResult;
    console.log(`\n🎯 总体结果: ${allPassed ? '✅ 所有测试通过' : '❌ 存在问题'}`);
}

// 运行测试
testPasswordScenarios().catch(console.error);
